// Attribute Values Section for associating attribute values with product variants
// Handles bulk operations and individual management

import React, { useState } from 'react'
import { FiPlus, FiTrash2, FiSave, FiX, FiTag, FiCheck } from 'react-icons/fi'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'


import {
  useDeleteVariantAttributeValue,
  useBulkAssociateVariantAttributeValues,
  useBulkUpdateVariantAttributeValueStatus,
} from '../../../../hooks/products-hooks/use-variant-attribute-values'

import {
  useVariantAttributeValues,
  useAttributeValuesByProductType,
  useReorderVariantAttributeValuesDragDrop
} from '../../../../hooks/products-hooks/use-variant-attribute-values'
import { Card, CardHeader, CardBody } from '../../../../components/ui/Card'
import { Button } from '../../../../components/ui/Button'

import { Modal } from '../../../../components/ui/Modal'
import { ButtonLoading, PageLoading } from '../../../../components/ui/LoadingSpinner'
import { Badge } from '../../../../components/ui/Badge'
import { DragIndicator } from '../../../../components/ui/DragIndicator'
import type { Product, ProductVariant } from '../../../../types/api-types'
import styles from './AttributeValuesSection.module.scss'





interface VariantAttributeValue {
  id: number
  attribute_value: {
    id: number
    attribute_value: string
    attribute: number
  }
  attribute_value_text: string
  is_active: boolean
  order: number
}

interface ProductTypeAttributeValue {
  attribute_id: number
  attribute_title: string
  values: Array<{
    id: number
    attribute_value: string
    is_active: boolean
  }>
}



interface AttributeValuesSectionProps {
  product: Product
}

const DND_ITEM_TYPE = 'ATTRIBUTE_VALUE'



export const AttributeValuesSection: React.FC<AttributeValuesSectionProps> = ({ product }) => {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [selectedAssociations, setSelectedAssociations] = useState<number[]>([])
  const [deletingAssociation, setDeletingAssociation] = useState<VariantAttributeValue | null>(null)
  const [selectedNewAttributeValues, setSelectedNewAttributeValues] = useState<number[]>([])

  const variants = product.variants || []
  const { data: variantAttributeValues, isLoading: attributeValuesLoading } = useVariantAttributeValues(selectedVariant?.id || 0)

  // New API hooks for the updated functionality
  // const { data: variantAttributeValuesNew } = useVariantAttributeValuesByVariant(selectedVariant?.id || 0)
  const { data: productTypeAttributeValues, isLoading: productTypeAttributeValuesLoading } = useAttributeValuesByProductType(product.product_type || 0)

  const deleteAssociationMutation = useDeleteVariantAttributeValue()
  const bulkAssociateMutation = useBulkAssociateVariantAttributeValues()
  const bulkUpdateStatusMutation = useBulkUpdateVariantAttributeValueStatus()
  const reorderMutation = useReorderVariantAttributeValuesDragDrop()

  console.log(variantAttributeValues)



  // Set first variant as selected by default
  React.useEffect(() => {
    if (variants && variants.length > 0 && !selectedVariant) {
      setSelectedVariant(variants[0])
    }
  }, [variants, selectedVariant])

  // Draggable Attribute Card Component
  const DraggableAttributeCard: React.FC<{ association: VariantAttributeValue; index: number }> = ({ association, index }) => {
    const [{ isDragging }, dragRef] = useDrag({
      type: DND_ITEM_TYPE,
      item: { id: association.id, index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    })

    const [{ isOver }, dropRef] = useDrop({
      accept: DND_ITEM_TYPE,
      drop: (item: { id: number; index: number }) => {
        if (item.id !== association.id) {
          handleAttributeValueReorder(item.id, association.id)
        }
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
    })

    const dragDropRef = (node: HTMLDivElement | null) => {
      dragRef(node)
      dropRef(node)
    }

    return (
      <div
        ref={dragDropRef}
        className={`${styles.attributeCard} ${styles.inlineCard} ${isDragging ? styles.dragging : ''} ${isOver ? styles.dropTarget : ''}`}
        style={{ opacity: isDragging ? 0.5 : 1 }}
      >
        {/* Checkbox */}
        <input
          type="checkbox"
          checked={selectedAssociations.includes(association.id)}
          onChange={() => toggleAssociationSelection(association.id)}
          className={styles.checkbox}
        />
        {/* Attribute Info */}
        {/* <div className={styles.attributeDetailsInline}> */}
        {/* <div className={styles.attributeHeaderInline}> */}
        <h4>{association.attribute_value_text}</h4>
        <Badge variant={association.is_active ? 'success' : 'error'}>
          {association.is_active ? 'Active' : 'Inactive'}
        </Badge>
        {/* </div> */}
        <p className={styles.attributeValue}>{association.attribute_value.attribute_value}</p>
        <span className={styles.orderInfo}>Order: {association.order}</span>
        {/* </div> */}
        {/* Delete Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setDeletingAssociation(association)}
          className={styles.deleteButtonInline}
          title="Remove Attribute Value"
        >
          <FiTrash2 />
        </Button>
        {/* Drag Handle (rightmost) */}
        <span className={styles.dragHandleInline} title="Drag to reorder attribute values">
          <DragIndicator variant="compact" />
        </span>
      </div>
    )
  }



  const handleDeleteAssociation = async () => {
    if (!deletingAssociation) return

    try {
      await deleteAssociationMutation.mutateAsync(deletingAssociation.id)
      setDeletingAssociation(null)
    } catch (error) {
      console.error('Failed to delete association:', error)
    }
  }

  const handleBulkStatusUpdate = async (isActive: boolean) => {
    if (selectedAssociations.length === 0) return

    try {
      await bulkUpdateStatusMutation.mutateAsync({
        association_ids: selectedAssociations,
        is_active: isActive,
      })
      setSelectedAssociations([])
    } catch (error) {
      console.error('Failed to update status:', error)
    }
  }

  const toggleAssociationSelection = (associationId: number) => {
    setSelectedAssociations(prev =>
      prev.includes(associationId)
        ? prev.filter(id => id !== associationId)
        : [...prev, associationId]
    )
  }

  const selectAllAssociations = () => {
    if (!variantAttributeValues) return
    setSelectedAssociations(variantAttributeValues.map((assoc: VariantAttributeValue) => assoc.id))
  }

  const clearSelection = () => {
    setSelectedAssociations([])
  }

  // Handler for adding new attribute values
  const handleAddAttributeValues = async () => {
    if (!selectedVariant || selectedNewAttributeValues.length === 0) return

    try {
      await bulkAssociateMutation.mutateAsync({
        product_variant_id: selectedVariant.id,
        attribute_value_ids: selectedNewAttributeValues
      })
      setIsAddModalOpen(false)
      setSelectedNewAttributeValues([])
    } catch (error) {
      console.error('Failed to associate attribute values:', error)
    }
  }

  // Handler for reordering attribute values
  const handleAttributeValueReorder = async (draggedAssociationId: number, targetAssociationId: number) => {
    if (!variantAttributeValues || !selectedVariant || draggedAssociationId === targetAssociationId) return

    // Create new order based on current associations
    const currentAssociations = [...variantAttributeValues].sort((a, b) => a.order - b.order)
    const draggedIndex = currentAssociations.findIndex(assoc => assoc.id === draggedAssociationId)
    const targetIndex = currentAssociations.findIndex(assoc => assoc.id === targetAssociationId)

    if (draggedIndex === -1 || targetIndex === -1) return

    // Reorder the array
    const reorderedAssociations = [...currentAssociations]
    const [draggedItem] = reorderedAssociations.splice(draggedIndex, 1)
    reorderedAssociations.splice(targetIndex, 0, draggedItem)

    // Create ordered IDs array
    const orderedIds = reorderedAssociations.map(assoc => assoc.id)

    try {
      await reorderMutation.mutateAsync({
        productVariantId: selectedVariant.id,
        orderedIds
      })
    } catch (error) {
      console.error('Failed to reorder attribute values:', error)
    }
  }



  if (variants.length === 0) {
    return (
      <Card>
        <CardHeader>
          <h2>Attribute Values</h2>
          <p>Associate attribute values with product variants</p>
        </CardHeader>
        <CardBody>
          <div className={styles.emptyState}>
            <FiTag size={48} />
            <h3>No Variants Available</h3>
            <p>You need to create product variants before you can associate attribute values.</p>
          </div>
        </CardBody>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className={styles.header}>
            <div>
              <h2>Attribute Values</h2>
              <p>Associate attribute values with product variants</p>
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={() => setIsAddModalOpen(true)}
              className={styles.addAttributeButton}
              // onClick={() => setIsBulkModalOpen(true)}
              disabled={!selectedVariant}
            >
              <FiPlus />
              Add Attributes
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {/* Variant Selector */}
          <div className={styles.variantSelector}>
            <label>Select Variant:</label>
            <div className={styles.variantTabs}>
              {variants.map((variant) => (
                <button
                  key={variant.id}
                  className={`${styles.variantTab} ${selectedVariant?.id === variant.id ? styles.active : ''
                    }`}
                  onClick={() => setSelectedVariant(variant)}
                >
                  <span className={styles.variantSku}>{variant.price_label_title}</span>
                  <Badge variant={variant.is_active ? 'success' : 'error'} size="sm">
                    {variant.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </button>
              ))}
            </div>
          </div>

          {/* Attribute Values Management */}
          {selectedVariant && (
            <div className={styles.attributesSection}>
              <div className={styles.sectionHeader}>
                <h3>Attributes for {selectedVariant.price_label_title}</h3>
                <div className={styles.bulkActions}>
                  {selectedAssociations.length > 0 && (
                    <>
                      <span className={styles.selectionCount}>
                        {selectedAssociations.length} selected
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBulkStatusUpdate(true)}
                        disabled={bulkUpdateStatusMutation.isPending}
                      >
                        <FiCheck />
                        Activate
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBulkStatusUpdate(false)}
                        disabled={bulkUpdateStatusMutation.isPending}
                      >
                        <FiX />
                        Deactivate
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearSelection}
                      >
                        Clear
                      </Button>
                    </>
                  )}
                  {variantAttributeValues && variantAttributeValues.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={selectAllAssociations}
                    >
                      Select All
                    </Button>
                  )}
                </div>
              </div>

              {attributeValuesLoading ? (
                <div className={styles.loadingState}>
                  <PageLoading message="Loading attribute values..." />
                </div>
              ) : !variantAttributeValues || variantAttributeValues.length === 0 ? (
                <div className={styles.emptyAttributes}>
                  <FiTag size={48} />
                  <h4>No Attributes</h4>
                  <p>No attribute values have been associated with this variant yet.</p>
                  <Button
                    variant="outline"
                    // onClick={() => setIsBulkModalOpen(true)}
                    onClick={() => setIsAddModalOpen(true)}
                  >
                    <FiPlus />
                    Add First Attribute
                  </Button>
                </div>
              ) : (
                <DndProvider backend={HTML5Backend}>
                  <div className={styles.attributesList}>
                    {variantAttributeValues.map((association: VariantAttributeValue, index: number) => (
                      <DraggableAttributeCard key={association.id} association={association} index={index} />
                    ))}

                    {/* Add Attribute Value Button */}
                    <div className={styles.addAttributeSection}>
                      <Button
                        variant="outline"
                        onClick={() => setIsAddModalOpen(true)}
                        className={styles.addAttributeButton}
                      >
                        <FiPlus />
                        Add attribute value
                      </Button>
                    </div>
                  </div>
                </DndProvider>
              )}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Bulk Associate Modal */}


      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingAssociation}
        onClose={() => setDeletingAssociation(null)}
        title="Remove Attribute"
        size="sm"
      >
        <div className={styles.deleteModal}>
          {deletingAssociation && (
            <>
              <p>
                Are you sure you want to remove the attribute{' '}
                <strong>
                  {deletingAssociation.attribute_value.attribute}: {deletingAssociation.attribute_value.attribute_value}
                </strong>{' '}
                from this variant? This action cannot be undone.
              </p>
              <div className={styles.modalActions}>
                <Button
                  variant="outline"
                  onClick={() => setDeletingAssociation(null)}
                  disabled={deleteAssociationMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={handleDeleteAssociation}
                  disabled={deleteAssociationMutation.isPending}
                >
                  <ButtonLoading
                    isLoading={deleteAssociationMutation.isPending}
                    loadingText="Removing..."
                  >
                    <FiTrash2 />
                    Remove
                  </ButtonLoading>
                </Button>
              </div>
            </>
          )}
        </div>
      </Modal>

      {/* Add Attribute Values Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => {
          setIsAddModalOpen(false)
          setSelectedNewAttributeValues([])
        }}
        title="Add Attribute Values"
        size="lg"
      >
        <div className={styles.addModalForm}>
          {/* Modal Header with Product and Variant Info */}
          <div className={styles.modalHeader}>
            <h3 className={styles.productTitle}>{product.title}</h3>
            <p className={styles.variantInfo}>
              Variant: {selectedVariant?.price_label_title || selectedVariant?.sku}
            </p>
          </div>

          {/* Attribute Values Selection (Checkboxes, Card-style, Grid) */}
          <div className={styles.attributeSelection}>
            <label className={styles.selectLabel}>Select Attribute Values:</label>
            {productTypeAttributeValuesLoading ? (
              <div className={styles.loadingState}>
                <PageLoading message="Loading attribute values..." />
              </div>
            ) : productTypeAttributeValues?.attributes ? (
              <div className={styles.attributeGroups}>
                {productTypeAttributeValues.attributes.map((attr: ProductTypeAttributeValue) => (
                  <div key={attr.attribute_id} className={styles.attributeGroup}>
                    <div className={styles.attributeGroupTitle}>{attr.attribute_title}</div>
                    <div className={styles.attributeValuesList}>
                      {attr.values.map((value) => {
                        // Always check if this value is already associated with the selected variant
                        // If so, it should be checked and disabled
                        const isAlreadyAssociated = variantAttributeValues?.some(
                          (assoc: VariantAttributeValue) => {
                            return assoc.attribute_value?.id === value.id
                          }
                        ) ?? false
                        const isChecked = isAlreadyAssociated || selectedNewAttributeValues.includes(value.id)
                        return (
                          <label key={value.id} className={styles.attributeValueItem}>
                            <input
                              type="checkbox"
                              className={styles.attributeValueCheckbox}
                              value={value.id}
                              checked={isChecked}
                              disabled={isAlreadyAssociated}
                              onChange={e => {
                                if (isAlreadyAssociated) return
                                if (e.target.checked) {
                                  setSelectedNewAttributeValues(prev => [...prev, value.id])
                                } else {
                                  setSelectedNewAttributeValues(prev => prev.filter((id: number) => id !== value.id))
                                }
                              }}
                            />
                            <span className={styles.attributeValueLabel}>{value.attribute_value}</span>
                          </label>
                        )
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles.emptyState}>
                <p>No attribute values available for this product type.</p>
              </div>
            )}
          </div>

          {/* Modal Actions */}
          <div className={styles.modalActions}>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddModalOpen(false)
                setSelectedNewAttributeValues([])
              }}
              disabled={bulkAssociateMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleAddAttributeValues}
              disabled={bulkAssociateMutation.isPending || selectedNewAttributeValues.length === 0}
            >
              <ButtonLoading
                isLoading={bulkAssociateMutation.isPending}
                loadingText="Adding..."
              >
                <FiPlus />
                Add Selected Values
              </ButtonLoading>
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default AttributeValuesSection
