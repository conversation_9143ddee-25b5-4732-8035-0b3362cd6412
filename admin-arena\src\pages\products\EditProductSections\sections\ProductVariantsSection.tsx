// Product Variants Section for managing product variants
// Handles CRUD operations for variants with individual Save/Cancel buttons

import React, { useState } from 'react'
import { useForm, Controller } from 'react-hook-form'
import type { UseFormReturn } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiPlus, FiEdit, FiTrash2, FiSave, FiX } from 'react-icons/fi'
import Select from 'react-select'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { useAttributeValues } from '../../../../hooks/products-hooks/use-attribute-values'
import {
  useCreateVariant,
  useUpdateProductVariant,
  useDeleteVariant,
  useReorderProductVariantsDragDrop
} from '../../../../hooks/products-hooks/use-product-variants'
import { Card, CardHeader, CardBody } from '../../../../components/ui/Card'
import { Button } from '../../../../components/ui/Button'
import { Input } from '../../../../components/ui/Input'
import { Switch } from '../../../../components/ui/Switch'
import { ButtonLoading } from '../../../../components/ui/LoadingSpinner'
import { Modal } from '../../../../components/ui/Modal'
import { Badge } from '../../../../components/ui/Badge'
import { DragIndicator } from '../../../../components/ui/DragIndicator'
import type { Product, ProductVariant } from '../../../../types/api-types'
import styles from './ProductVariantsSection.module.scss'

const variantSchema = z.object({
  price: z.string().min(1, 'Price is required'),
  price_label: z.number().optional(),
  sku: z.string().min(1, 'SKU is required'),
  stock_qty: z.number().min(0, 'Stock quantity must be 0 or greater'),
  is_active: z.boolean(),
  weight: z.number().optional(),
  condition: z.string().optional(),
})

type VariantFormData = z.infer<typeof variantSchema>

interface ProductVariantsSectionProps {
  product: Product
}

const DND_ITEM_TYPE = 'VARIANT'

export const ProductVariantsSection: React.FC<ProductVariantsSectionProps> = ({ product }) => {
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [deletingVariant, setDeletingVariant] = useState<ProductVariant | null>(null)

  const variants = product.variants || []
  const { data: attributeValues } = useAttributeValues()
  const createVariantMutation = useCreateVariant()
  const updateVariantMutation = useUpdateProductVariant()
  const deleteVariantMutation = useDeleteVariant()
  const reorderVariantsMutation = useReorderProductVariantsDragDrop()

  const createForm = useForm<VariantFormData>({
    resolver: zodResolver(variantSchema),
    defaultValues: {
      price: '',
      sku: '',
      stock_qty: 0,
      is_active: true,
      weight: undefined,
      condition: 'New',
    },
  })

  const editForm = useForm<VariantFormData>({
    resolver: zodResolver(variantSchema),
  })

  // Options for attribute values (price labels)
  const attributeValueOptions = attributeValues?.map(av => ({
    value: av.id,
    label: av.attribute_value
  })) || []

  const conditionOptions = [
    { value: 'New', label: 'New' },
    { value: 'Used', label: 'Used' },
    { value: 'Refurbished', label: 'Refurbished' },
  ]

  const handleCreateVariant = async (data: VariantFormData) => {
    try {
      await createVariantMutation.mutateAsync({
        ...data,
        price: Number(data.price), // Convert price to number
        product: product.id,
      })
      setIsCreateModalOpen(false)
      createForm.reset()
    } catch (error) {
      console.error('Failed to create variant:', error)
    }
  }

  const handleEditVariant = (variant: ProductVariant) => {
    setEditingVariant(variant)
    editForm.reset({
      price: variant.price.toString(),
      price_label: variant.price_label || undefined,
      sku: variant.sku,
      stock_qty: variant.stock_qty,
      is_active: variant.is_active,
      weight: variant.weight || undefined,
      condition: variant.condition || 'New',
    })
  }

  const handleUpdateVariant = async (data: VariantFormData) => {
    if (!editingVariant) return

    try {
      await updateVariantMutation.mutateAsync({
        id: editingVariant.id,
        data: {
          ...data,
          price: Number(data.price), // Convert price to number
        },
      })
      setEditingVariant(null)
    } catch (error) {
      console.error('Failed to update variant:', error)
    }
  }

  const handleDeleteVariant = async () => {
    if (!deletingVariant) return

    try {
      await deleteVariantMutation.mutateAsync(deletingVariant.id)
      setDeletingVariant(null)
    } catch (error) {
      console.error('Failed to delete variant:', error)
    }
  }

  const handleCancelEdit = () => {
    setEditingVariant(null)
    editForm.reset()
  }

  // Handler for reordering variants
  const handleVariantReorder = async (draggedVariantId: number, targetVariantId: number) => {
    if (draggedVariantId === targetVariantId) return

    // Create new order based on current variants
    const currentVariants = [...variants].sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
    const draggedIndex = currentVariants.findIndex(variant => variant.id === draggedVariantId)
    const targetIndex = currentVariants.findIndex(variant => variant.id === targetVariantId)

    if (draggedIndex === -1 || targetIndex === -1) return

    // Reorder the array
    const reorderedVariants = [...currentVariants]
    const [draggedItem] = reorderedVariants.splice(draggedIndex, 1)
    reorderedVariants.splice(targetIndex, 0, draggedItem)

    // Create ordered IDs array
    const orderedIds = reorderedVariants.map(variant => variant.id)

    try {
      await reorderVariantsMutation.mutateAsync({
        productId: product.id,
        orderedIds
      })
    } catch (error) {
      console.error('Failed to reorder variants:', error)
    }
  }

  // Draggable Variant Card Component
  const DraggableVariantCard: React.FC<{
    variant: ProductVariant
    index: number
    editingVariant: ProductVariant | null
    editForm: UseFormReturn<VariantFormData>
    attributeValueOptions: Array<{ value: string | number; label: string }>
    conditionOptions: Array<{ value: string; label: string }>
    onEdit: (variant: ProductVariant) => void
    onUpdate: (data: VariantFormData) => void
    onCancel: () => void
    onDelete: (variant: ProductVariant) => void
    onReorder: (draggedId: number, targetId: number) => void
    updateMutation: { isPending: boolean }
  }> = ({
    variant,
    index,
    editingVariant,
    editForm,
    attributeValueOptions,
    conditionOptions,
    onEdit,
    onUpdate,
    onCancel,
    onDelete,
    onReorder,
    updateMutation
  }) => {
      const [{ isDragging }, dragRef] = useDrag({
        type: DND_ITEM_TYPE,
        item: { id: variant.id, index },
        collect: (monitor) => ({
          isDragging: monitor.isDragging(),
        }),
      })

      const [{ isOver }, dropRef] = useDrop({
        accept: DND_ITEM_TYPE,
        drop: (item: { id: number; index: number }) => {
          if (item.id !== variant.id) {
            onReorder(item.id, variant.id)
          }
        },
        collect: (monitor) => ({
          isOver: monitor.isOver(),
        }),
      })

      return (
        <div
          ref={(node) => { dropRef(node) }}
          className={`${styles.variantCard} ${isDragging ? styles.dragging : ''} ${isOver ? styles.dropTarget : ''}`}
          style={{ opacity: isDragging ? 0.5 : 1, position: 'relative' }}
        >
          <div className={styles.dragHandleContainer}>
            <span ref={(node) => { dragRef(node) }} className={styles.dragHandle}>
              <DragIndicator variant="compact" tooltip="Drag to reorder variants" />
            </span>
          </div>
          {editingVariant?.id === variant.id ? (
            <form
              onSubmit={editForm.handleSubmit(onUpdate)}
              className={styles.editForm}
            >
              <div className={styles.formGrid}>
                <div className={styles.formGroup}>
                  <label>Price *</label>
                  <Input
                    {...editForm.register('price')}
                    error={editForm.formState.errors.price?.message}
                    placeholder="0.00"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>SKU *</label>
                  <Input
                    {...editForm.register('sku')}
                    error={editForm.formState.errors.sku?.message}
                    placeholder="Enter SKU"
                  />
                </div>
              </div>

              <div className={styles.formGrid}>
                <div className={styles.formGroup}>
                  <label>Stock Quantity *</label>
                  <Input
                    type="number"
                    {...editForm.register('stock_qty', { valueAsNumber: true })}
                    error={editForm.formState.errors.stock_qty?.message}
                    placeholder="0"
                    min="0"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Weight (grams)</label>
                  <Input
                    type="number"
                    {...editForm.register('weight', { valueAsNumber: true })}
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>

              <div className={styles.formGrid}>
                <div className={styles.formGroup}>
                  <label>Price Label</label>
                  <Controller
                    name="price_label"
                    control={editForm.control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={attributeValueOptions}
                        placeholder="Select price label..."
                        classNamePrefix="react-select"
                        value={attributeValueOptions.find(opt => opt.value === field.value) || null}
                        onChange={opt => field.onChange(opt ? opt.value : undefined)}
                        styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                        isClearable
                      />
                    )}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Condition</label>
                  <Controller
                    name="condition"
                    control={editForm.control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={conditionOptions}
                        placeholder="Select condition..."
                        classNamePrefix="react-select"
                        value={conditionOptions.find(opt => opt.value === field.value) || null}
                        onChange={opt => field.onChange(opt ? opt.value : 'New')}
                        styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                      />
                    )}
                  />
                </div>
              </div>

              <div className={styles.switchGroup}>
                <Controller
                  name="is_active"
                  control={editForm.control}
                  render={({ field }) => (
                    <Switch
                      checked={field.value}
                      onChange={field.onChange}
                      label="Is Active"
                    />
                  )}
                />
              </div>

              <div className={styles.formActions}>
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={updateMutation.isPending}
                >
                  <FiX />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  disabled={updateMutation.isPending}
                >
                  <ButtonLoading
                    isLoading={updateMutation.isPending}
                    loadingText="Saving..."
                  >
                    <FiSave />
                    Save Changes
                  </ButtonLoading>
                </Button>
              </div>
            </form>
          ) : (
            <div className={styles.variantDisplay}>
              <div className={styles.variantInfo}>
                <div className={styles.variantHeader}>
                  <h4>{variant.price_label_title || variant.sku}</h4>
                  <div className={styles.variantBadges}>
                    <Badge variant={variant.is_active ? 'success' : 'error'}>
                      {variant.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                    <span className={styles.orderBadge}>Order: {variant.order}</span>
                  </div>
                  <div className={styles.variantActions}>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(variant)}
                    >
                      <FiEdit />
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(variant)}
                      className={styles.deleteButton}
                    >
                      <FiTrash2 />
                      Delete
                    </Button>
                  </div>
                </div>
                <div className={styles.variantDetails}>
                  <div className={styles.detailItem}>
                    <span className={styles.label}><strong>Price:</strong></span>
                    <span className={styles.value}>${variant.price}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.label}><strong>SKU:</strong></span>
                    <span className={styles.value}>{variant.sku}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.label}><strong>Stock:</strong></span>
                    <span className={styles.value}>{variant.stock_qty}</span>
                  </div>
                  {variant.weight && (
                    <div className={styles.detailItem}>
                      <span className={styles.label}><strong>Weight:</strong></span>
                      <span className={styles.value}>{variant.weight}g</span>
                    </div>
                  )}
                  <div className={styles.detailItem}>
                    <span className={styles.label}><strong>Condition:</strong></span>
                    <span className={styles.value}>{variant.condition || 'New'}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )
    }

  return (
    <>
      <Card>
        <CardHeader>
          <div className={styles.header}>
            <div>
              <h2>Product Variants</h2>
              <p>Manage different variations of this product</p>
            </div>
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <FiPlus />
              Add Variant
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {variants.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No variants found for this product.</p>
              <Button
                variant="outline"
                onClick={() => setIsCreateModalOpen(true)}
              >
                <FiPlus />
                Create First Variant
              </Button>
            </div>
          ) : (
            <DndProvider backend={HTML5Backend}>
              <div className={styles.variantsList}>
                {variants.map((variant, index) => (
                  <DraggableVariantCard
                    key={variant.id}
                    variant={variant}
                    index={index}
                    editingVariant={editingVariant}
                    editForm={editForm}
                    attributeValueOptions={attributeValueOptions}
                    conditionOptions={conditionOptions}
                    onEdit={handleEditVariant}
                    onUpdate={handleUpdateVariant}
                    onCancel={handleCancelEdit}
                    onDelete={setDeletingVariant}
                    onReorder={handleVariantReorder}
                    updateMutation={updateVariantMutation}
                  />
                ))}
              </div>
            </DndProvider>
          )}
        </CardBody>
      </Card>

      {/* Create Variant Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Variant"
        size="lg"
      >
        <form onSubmit={createForm.handleSubmit(handleCreateVariant)} className={styles.modalForm}>
          <div className={styles.formGrid}>
            <div className={styles.formGroup}>
              <label>SKU *</label>
              <Input
                {...createForm.register('sku')}
                error={createForm.formState.errors.sku?.message}
                placeholder="Enter SKU"
              />
            </div>
            <div className={styles.formGroup}>
              <label>Price *</label>
              <Input
                {...createForm.register('price')}
                error={createForm.formState.errors.price?.message}
                placeholder="0.00"
              />
            </div>
          </div>

          <div className={styles.formGrid}>
            <div className={styles.formGroup}>
              <label>Stock Quantity *</label>
              <Input
                type="number"
                {...createForm.register('stock_qty', { valueAsNumber: true })}
                error={createForm.formState.errors.stock_qty?.message}
                placeholder="0"
                min="0"
              />
            </div>
            <div className={styles.formGroup}>
              <label>Weight (grams)</label>
              <Input
                type="number"
                {...createForm.register('weight', { valueAsNumber: true })}
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          <div className={styles.formGrid}>
            <div className={styles.formGroup}>
              <label>Price Label</label>
              <Controller
                name="price_label"
                control={createForm.control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={attributeValueOptions}
                    placeholder="Select price label..."
                    classNamePrefix="react-select"
                    value={attributeValueOptions.find(opt => opt.value === field.value) || null}
                    onChange={opt => field.onChange(opt ? opt.value : undefined)}
                    styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                    isClearable
                  />
                )}
              />
            </div>
            <div className={styles.formGroup}>
              <label>Condition</label>
              <Controller
                name="condition"
                control={createForm.control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={conditionOptions}
                    placeholder="Select condition..."
                    classNamePrefix="react-select"
                    value={conditionOptions.find(opt => opt.value === field.value) || null}
                    onChange={opt => field.onChange(opt ? opt.value : 'New')}
                    styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                  />
                )}
              />
            </div>
          </div>

          <div className={styles.switchGroup}>
            <Controller
              name="is_active"
              control={createForm.control}
              render={({ field }) => (
                <Switch
                  checked={field.value}
                  onChange={field.onChange}
                  label="Is Active"
                />
              )}
            />
          </div>

          <div className={styles.modalActions}>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
              disabled={createVariantMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={createVariantMutation.isPending}
            >
              <ButtonLoading
                isLoading={createVariantMutation.isPending}
                loadingText="Creating..."
              >
                <FiPlus />
                Create Variant
              </ButtonLoading>
            </Button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingVariant}
        onClose={() => setDeletingVariant(null)}
        title="Delete Variant"
        size="sm"
      >
        <div className={styles.deleteModal}>
          <p>Are you sure you want to delete this variant?</p>
          <div className={styles.variantInfo}>
            <strong>SKU:</strong> {deletingVariant?.sku}
          </div>
          <div className={styles.modalActions}>
            <Button
              variant="outline"
              onClick={() => setDeletingVariant(null)}
              disabled={deleteVariantMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteVariant}
              disabled={deleteVariantMutation.isPending}
            >
              <ButtonLoading
                isLoading={deleteVariantMutation.isPending}
                loadingText="Deleting..."
              >
                <FiTrash2 />
                Delete Variant
              </ButtonLoading>
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}

